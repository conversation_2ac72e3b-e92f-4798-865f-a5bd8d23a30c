import {LatestApi} from "../LatestApi";

export interface iLogs {
    id?: number;
    created_at?: Date;
    updated_at?: Date;
    ticker_internal_id?: number;
    latest_api: LatestApi;
    latest_log: string;
    latest_update: Date;
    quantity_of_balance_sheet_year?: number;
    quantity_of_balance_sheet_quarter?: number;
    quantity_of_cash_flow_year?: number;
    quantity_of_cash_flow_quarter?: number;
    quantity_of_income_statement_year?: number;
    quantity_of_income_statement_quarter?: number;
    quantity_of_dividends?: number;
    quanitty_of_splits?: number;
    start_of_balance_sheet_year: Date | null;
    end_of_balance_sheet_year: Date | null;
    start_of_cash_flow_year: Date | null;
    end_of_cash_flow_year: Date | null;
    start_of_income_statement_year: Date | null;
    end_of_income_statement_year: Date | null;
    start_of_balance_sheet_quarter: Date | null;
    end_of_balance_sheet_quarter: Date | null;
    start_of_cash_flow_quarter: Date | null;
    end_of_cash_flow_quarter: Date | null;
    start_of_income_statement_quarter: Date | null;
    end_of_income_statement_quarter: Date | null;
}

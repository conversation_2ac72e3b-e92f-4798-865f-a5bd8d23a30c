import {Document_type_year_or_quarter} from "../DocumentTypeYearorQuarterly";

export interface iIncomeStatement {
    id?: number;
    ticker_internal_id: number;
    created_at: Date;
    updated_at: Date;
    document_current: number;
    document_type_year_or_quarter: Document_type_year_or_quarter;
    document_date: string;
    currency_symbol: string;
    research_development: number;
    effect_of_accounting_charges: number;
    income_before_tax: number;
    minority_interest: number;
    net_income: number;
    selling_general_administrative: number;
    selling_and_marketing_expenses: number;
    gross_profit: number;
    reconciled_depreciation: number;
    ebit: number;
    ebitda: number;
    depreciation_and_amortization: number;
    non_operating_income_net_other: number;
    operating_income: number;
    other_operating_expenses: number;
    interest_expense: number;
    tax_provision: number;
    interest_income: number;
    net_interest_income: number;
    extraordinary_items: number;
    non_recurring: number;
    other_items: number;
    income_tax_expense: number;
    total_revenue: number;
    total_operating_expenses: number;
    cost_of_revenue: number;
    total_other_income_expense_net: number;
    discontinued_operations: number;
    net_income_from_continuing_ops: number;
    net_income_applicable_to_common_shares: number;
    preferred_stock_and_other_adjustments: number;
    eps_diluted_current: number;
    eps_diluted_last_date: number;
}

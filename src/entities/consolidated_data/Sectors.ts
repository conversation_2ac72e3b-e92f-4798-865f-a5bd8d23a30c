import {DataTypes, Model, Optional} from "sequelize";
import {sequelize as conn} from "../../lib/database";
import {iSector} from "./interfaces/iSector";

export interface SectorInput extends Optional<iSector, "id" | "created_at" | "updated_at" | "en" | "fr" | "pt"> {}

export class Sector extends Model<iSector, SectorInput> implements iSector {
    declare id?: number;
    declare created_at?: Date;
    declare updated_at?: Date;
    declare sector_id: number;
    declare en: string;
    declare fr: string;
    declare pt: string;
}

export const sectors = Sector.init(
    {
        id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            primaryKey: true,
        },
        created_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        sector_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        en: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        fr: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        pt: {
            type: DataTypes.STRING,
            allowNull: false,
        },
    },
    {
        timestamps: false,
        sequelize: conn,
        tableName: "sectors",
        hooks: {
            beforeCreate: (record) => {
                record.dataValues.created_at = new Date();
                record.dataValues.updated_at = new Date();
            },
            beforeUpdate: (record) => {
                record.dataValues.updated_at = new Date();
            },
        },
    },
);

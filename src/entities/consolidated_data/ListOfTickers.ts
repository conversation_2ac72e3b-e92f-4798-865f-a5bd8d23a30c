import {DataTypes, Model, Optional} from "sequelize";
import {sequelize as conn} from "../../lib/database";
import {iTickers} from "./interfaces/iTickers";

export interface TickersInput extends Optional<iTickers, "id" | "name" | "primary_ticker_eodhd" | "created_at" | "updated_at"> {}
export interface TickersOuput extends Required<iTickers> {}

class ListOfTickers extends Model<iTickers, TickersInput> implements iTickers {
    declare id?: number;
    declare symbol_code: string;
    declare country_code: string;
    declare exchange_code: string;
    declare name?: string;
    declare primary_ticker_eodhd: string;
    declare primary_ticker_twelve_data?: string;
    declare log_eodhd?: string;
    declare is_enable: number;
    declare currency_code: string;
    declare created_at?: Date;
    declare updated_at?: Date;
    declare sector_id?: number;
    declare isin?: string;
    declare type: string;
    declare another_symbol_codes?: string;
    declare url_endpoint?: string;
    declare fundamental_data_last_updated?: Date;
    declare reason_not_enable?: string;
}

const tickers = ListOfTickers.init(
    {
        id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            primaryKey: true,
        },
        symbol_code: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        country_code: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        exchange_code: {
            type: DataTypes.STRING,
            allowNull: false,
            defaultValue: "",
        },
        name: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        primary_ticker_eodhd: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        primary_ticker_twelve_data: {
            type: DataTypes.STRING,
            allowNull: true,
        },

        log_eodhd: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        is_enable: {
            type: DataTypes.INTEGER,
            defaultValue: 1,
            allowNull: true,
        },
        created_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        currency_code: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        sector_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        isin: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        type: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        another_symbol_codes: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        url_endpoint: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        fundamental_data_last_updated: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        reason_not_enable: {
            type: DataTypes.STRING,
            allowNull: true,
        },
    },
    {
        timestamps: false,
        sequelize: conn,
        tableName: "list_of_tickers",
        hooks: {
            beforeCreate: (record) => {
                record.dataValues.created_at = new Date();
                record.dataValues.updated_at = new Date();
            },
            beforeUpdate: (record) => {
                record.dataValues.updated_at = new Date();
            },
        },
    },
);

export {ListOfTickers, tickers};

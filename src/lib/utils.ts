import {connectAgendaToMongoDB, isAgendaConnected} from "@/lib/agenda";
import dotenv from "dotenv";

dotenv.config();

const {LOG_LEVEL} = process.env;

export const checkIfAgendaIsConnected = async () => {
    // Ensure MongoDB connection is established first
    if (!isAgendaConnected()) {
        await connectAgendaToMongoDB();

        // If still not connected, return appropriate error
        if (!isAgendaConnected()) {
            throw new Error("Database connection not available");
        }
    }
};

// Log levels hierarchy: ERROR (0) < WARNING (1) < INFO (2) < DEBUG (3)
const LOG_LEVELS = {
    ERROR: 0,
    WARNING: 1,
    INFO: 2,
    DEBUG: 3,
} as const;

type LogLevel = keyof typeof LOG_LEVELS;

/**
 * Enhanced logging function that respects LOG_LEVEL environment variable
 * @param message - The message to log
 * @param level - The log level: 'DEBUG', 'INFO', 'WARNING', or 'ERROR'
 */
export const consoleLog = (message: string, functionName: string, level: LogLevel = "INFO") => {
    const currentLogLevel = (LOG_LEVEL?.toUpperCase() as LogLevel) || "INFO";
    const currentLevelValue = LOG_LEVELS[currentLogLevel] || LOG_LEVELS.INFO;
    const messageLevelValue = LOG_LEVELS[level];

    // Only log if the message level is at or above the current log level
    if (messageLevelValue <= currentLevelValue) {
        const timestamp = new Date().toISOString();
        const prefix = `[${timestamp}] [${level.toUpperCase()}] [${functionName}]`;

        switch (level) {
            case "ERROR":
                console.error(`${prefix} - ${message}`);
                break;
            case "WARNING":
                console.warn(`${prefix} - ${message}`);
                break;
            case "INFO":
                console.info(`${prefix} - ${message}`);
                break;
            case "DEBUG":
                console.log(`${prefix} - ${message}`);
                break;
            default:
                console.log(`${prefix} - ${message}`);
        }
    }
};

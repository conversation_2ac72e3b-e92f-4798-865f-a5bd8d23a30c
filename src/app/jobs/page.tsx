"use client";

import type React from "react";
import Link from "next/link";
import styles from "./page.module.css";
import {useEffect, useState} from "react";
import axios from "axios";
import {JobDetails} from "@/utils/types/agenda/job";
import {ChevronUp, ChevronDown} from "lucide-react";

export default function JobsPage() {
    const [jobs, setJobs] = useState<JobDetails[]>([]);
    const [loading, setLoading] = useState(true);
    const [sortConfig, setSortConfig] = useState<{
        key: string;
        direction: "asc" | "desc";
    }>({key: "executionOrder", direction: "asc"});

    const handleSort = (key: string) => {
        let direction: "asc" | "desc" = "asc";
        if (sortConfig.direction === "asc") {
            direction = "desc";
        } else {
            direction = "asc";
        }
        setSortConfig({key, direction});
    };

    const getSortedData = (data: any[]) => {
        return [...data].sort((a, b) => {
            const aValue = a[sortConfig.key];
            const bValue = b[sortConfig.key];

            if (sortConfig.key === "lastRunAt" || sortConfig.key === "nextRunAt" || sortConfig.key === "lastFinishedAt") {
                const aDate = new Date(aValue).getTime();
                const bDate = new Date(bValue).getTime();
                return sortConfig.direction === "asc" ? aDate - bDate : bDate - aDate;
            }

            if (typeof aValue === "number") {
                return sortConfig.direction === "asc" ? aValue - bValue : bValue - aValue;
            }
            if (typeof aValue === "string") {
                return sortConfig.direction === "asc" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            }
            if (sortConfig.key === "disabled") {
                const aValueToSort = aValue ? 1 : 0;
                const bValueToSort = bValue ? 1 : 0;
                return sortConfig.direction === "asc" ? Number(aValueToSort) - Number(bValueToSort) : Number(bValueToSort) - Number(aValueToSort);
            }
            return 0;
        });
    };

    const sortedJobs = getSortedData(jobs);

    const SortableHeader = ({column, children}: {column: string; children: React.ReactNode}) => (
        <th className={styles.tableHead}>
            <button className={styles.sortButton} onClick={() => handleSort(column)}>
                {children}
                <span className={styles.sortIcon}>
                    {sortConfig.key === column ? (
                        sortConfig.direction === "asc" ? (
                            <ChevronUp className={styles.chevron} />
                        ) : (
                            <ChevronDown className={styles.chevron} />
                        )
                    ) : (
                        <div className={styles.chevronPlaceholder} />
                    )}
                </span>
            </button>
        </th>
    );

    useEffect(() => {
        const fetchJobs = async () => {
            try {
                const response = await axios.get("/api/jobs/list");
                setJobs(response.data);
            } catch (error) {
                console.error("Error fetching jobs:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchJobs();
    }, []);

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <h2 className={styles.title}>Jobs</h2>
            </div>
            <div className={styles.card}>
                <div className={styles.cardHeader}>
                    <h3 className={styles.cardTitle}>All Jobs</h3>
                    <p className={styles.cardDescription}>View and manage all your scheduled jobs</p>
                </div>
                <div className={styles.cardContent}>
                    <div className={styles.tableContainer}>
                        {loading ? (
                            <p>Loading jobs...</p>
                        ) : (
                            <table className={styles.table}>
                                <thead className={styles.tableHeader}>
                                    <tr>
                                        <SortableHeader column="executionOrder">Execution Order</SortableHeader>
                                        <SortableHeader column="_id">ID</SortableHeader>
                                        <SortableHeader column="name">Name</SortableHeader>
                                        <SortableHeader column="type">Type</SortableHeader>
                                        <SortableHeader column="repeatInterval">Repeat Interval</SortableHeader>
                                        <SortableHeader column="lastRunAt">Last Run</SortableHeader>
                                        <SortableHeader column="lastFinishedAt">Last Finished</SortableHeader>
                                        <SortableHeader column="nextRunAt">Next Run</SortableHeader>
                                        <SortableHeader column="disabled">Status</SortableHeader>
                                    </tr>
                                </thead>
                                <tbody>
                                    {sortedJobs.map((job) => (
                                        <tr key={job._id} className={styles.tableRow}>
                                            <td className={styles.tableCell}>
                                                <span className={styles.executionOrder}>#{job.executionOrder}</span>
                                            </td>
                                            <td className={styles.tableCell}>{job._id}</td>
                                            <td className={styles.tableCell}>
                                                <Link href={`/jobs/${job._id}`} className={styles.jobLink}>
                                                    {job.name}
                                                </Link>
                                            </td>
                                            <td className={styles.tableCell}>{job.type}</td>
                                            <td className={styles.tableCell}>{job.repeatInterval}</td>
                                            <td className={styles.tableCell}>{job.lastRunAt ? new Date(job.lastRunAt).toLocaleString() : "Not run yet"}</td>
                                            <td className={styles.tableCell}>{job.lastFinishedAt ? new Date(job.lastFinishedAt).toLocaleString() : "Not run yet"}</td>
                                            <td className={styles.tableCell}>{job.nextRunAt ? new Date(job.nextRunAt).toLocaleString() : "Not scheduled"}</td>
                                            <td className={styles.tableCell}>
                                                <span className={`${styles.badge} ${job.disabled ? styles.idle : styles.running}`}>{job.disabled ? "Disabled" : "Enabled"}</span>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}

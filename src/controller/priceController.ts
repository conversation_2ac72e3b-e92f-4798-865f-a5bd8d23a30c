import {RealTimePriceEODHD} from "@/repositories/implements/RealTimePriceDAO";
import {ListOfTickers} from "../entities/consolidated_data/ListOfTickers";
import {StatisticsOfTicker} from "../entities/consolidated_data/StatisticsOfTickers";
import {PriceTickerRepository} from "../repositories/implements/PriceTickerRepository";
import {addLogJobExecution} from "@/lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";
import {ReasonNotEnable} from "@/utils/types/EODHD/exchange_countries";

export async function getPrices(updatedTickers: ListOfTickers[], historyId?: string) {
    const repository = new PriceTickerRepository();
    return await repository.getPrice(updatedTickers, historyId);
}

export async function setPrices(tickersPrice: RealTimePriceEODHD[]) {
    const repository = new PriceTickerRepository();
    // Use the new batch method for better performance
    await repository.setPrices(tickersPrice);
}

export async function setInvalidPrices(tickersPrice: RealTimePriceEODHD[]) {
    const symbol_code = tickersPrice[0].code;
    const ticker_internal_id = tickersPrice[0].ticker_internal_id;
    try {
        addLogJobExecution(LogLevel.INFO, "setInvalidPrices", "Disabling ticker and deleting statistics", {}, ticker_internal_id || 0);
        let ticker: ListOfTickers | null = null;
        if (!ticker_internal_id && ticker_internal_id !== null) {
            ticker = await ListOfTickers.findOne({
                where: {
                    id: ticker_internal_id,
                },
            });
        } else {
            ticker = await ListOfTickers.findOne({
                where: {
                    primary_ticker_eodhd: symbol_code,
                },
            });
        }
        if (ticker) {
            await ticker.update({is_enable: 0, reason_not_enable: ReasonNotEnable.PRICE_NOT_FOUND});
            await ticker.save();
            await StatisticsOfTicker.destroy({
                where: {
                    ticker_internal_id: ticker.id,
                },
            });
            addLogJobExecution(LogLevel.ERROR, "setInvalidPrices", "Ticker disabled and statistics deleted", {}, ticker.id);
        }
    } catch (err: any) {
        console.log("Error when try to set invalid Price ", symbol_code, err.message);
        addLogJobExecution(LogLevel.ERROR, "setInvalidPrices", "Error when try to set invalid Price", {error: err instanceof Error ? err.message : String(err)}, ticker_internal_id || 0);
    }
}
